#include <iostream>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>

using namespace std;

int signal_count = 0;
int child_count = 0;

void hander(int signum)
{
    signal_count++;
    cout << "=== Signal handler called " << signal_count << " times ===" << endl;
    
    // 模拟延迟（这会导致信号丢失）
    sleep(2);
    
    int id;
    int recovered = 0;
    while((id = waitpid(0, nullptr, WNOHANG)) > 0)
    {
        recovered++;
        cout << "Recovered child pid: " << id << endl;
    }
    cout << "Total recovered in this call: " << recovered << endl;
    cout << "=== Signal handler finished ===" << endl;
}

int main()
{
    signal(SIGCHLD, hander);
    
    cout << "Creating 5 children quickly..." << endl;
    
    // 快速创建5个子进程，它们会几乎同时退出
    for(int i = 0; i < 5; i++)
    {
        pid_t id = fork();
        if(id == 0)
        {
            cout << "Child " << i << " (pid=" << getpid() << ") starting" << endl;
            sleep(1); // 短暂延迟后退出
            cout << "Child " << i << " (pid=" << getpid() << ") exiting" << endl;
            exit(0);
        }
        else if(id > 0)
        {
            child_count++;
        }
        else
        {
            perror("fork fail");
            exit(-1);
        }
    }
    
    cout << "All children created, waiting..." << endl;
    sleep(10); // 等待所有处理完成
    
    cout << "\n=== Final Statistics ===" << endl;
    cout << "Children created: " << child_count << endl;
    cout << "Signal handler called: " << signal_count << " times" << endl;
    
    return 0;
}
