#include <iostream>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>

using namespace std;

void hander(int signum)
{
    //模拟多个子进程都退出
    sleep(5);
    //打印通知
    cout << "Get a signal, signum is " << signum << endl;
    //处理退出的子进程
    int id;
    while((id = waitpid(0, nullptr, WNOHANG)) > 0)
    {
        cout << "One child quits, pid is " << id << endl;
    }
}

int main()
{
    //注册SIGCHID信号捕捉函数
    signal(SIGCHLD, hander);

    for(int i = 0; i < 10; i++)
    {
        pid_t id = fork();
        if(id == 0)
        {
            cout << "I am child, my pid is " << getpid() << endl;
            exit(0);
        }
        else if(id > 0)
        {
            cout << "I am father, my pid is " << getpid() << endl;
        }
        else
        {
            perror("fork fail:");
            exit(-1);
        }
        sleep(3);
    }
    sleep(3);
    return 0;
}
