#include <iostream>
#include <signal.h>
#include <string.h>
#include <unistd.h>

using namespace std;

void Print(sigset_t &set)
{
    for (int i = 1; i <= 31; i++)
    {
        if (sigismember(&set, i))
            cout << 1;
        else
            cout << 0;
    }
    cout << endl;
}

void handler(int signum)
{
    cout << "Get a signal, signum is " << signum << endl;
    for(int i = 0; i < 10; i++)
    {
        // 打印pending位图
        sigset_t pending;
        if (sigpending(&pending) == -1)
        {
            perror("sigpending fail: ");
            exit(1);
        }
        Print(pending);
        sleep(1);
    }
}



int main()
{
    // 定义struct sigaction结构
    struct sigaction act;
    // 初始化struct sigaction结构
    memset(&act, 0, sizeof(act));
    act.sa_handler = handler;
    if (sigaddset(&(act.sa_mask), 2) == -1)
    {
        perror("sigaddset fail:");
        exit(1);
    }

    // 注册2号信号捕捉函数
    if (sigaction(2, &act, nullptr) == -1)
    {
        perror("sigaction fail: ");
        exit(1);
    }

    while(true)
    {
        cout << "I'm a process, my pid is " << getpid() << endl;
        sleep(1);
    }

    return 0;
}